// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

class VCallParticipantJoinedEvent extends VEvent {
  final String roomId;
  final Map<String, dynamic> participant;
  final List<Map<String, dynamic>> allParticipants;

  VCallParticipantJoinedEvent({
    required this.roomId,
    required this.participant,
    required this.allParticipants,
  });
}