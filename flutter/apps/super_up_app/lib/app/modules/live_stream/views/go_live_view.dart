// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:v_platform/v_platform.dart';

import '../controllers/go_live_controller.dart';
import 'live_stream_view.dart';
import 'widgets/member_selection_sheet.dart';

class GoLiveView extends StatefulWidget {
  const GoLiveView({super.key});

  @override
  State<GoLiveView> createState() => _GoLiveViewState();
}

class _GoLiveViewState extends State<GoLiveView> {
  late final GoLiveController controller;

  @override
  void initState() {
    super.initState();
    controller = GetIt.I.get<GoLiveController>();
    controller.onInit();
  }

  @override
  void dispose() {
    // Don't dispose the singleton controller, just reset its state
    controller.resetController();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Check if running on web - live streaming not supported
    if (VPlatforms.isWeb) {
      return CupertinoPageScaffold(
        navigationBar: const CupertinoNavigationBar(
          middle: Text('Go Live'),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                CupertinoIcons.videocam,
                size: 64,
                color: CupertinoColors.systemGrey,
              ),
              const SizedBox(height: 16),
              const Text(
                'Live streaming is not supported on web',
                style: TextStyle(
                  fontSize: 18,
                  color: CupertinoColors.systemGrey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              CupertinoButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }

    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text(
          'Go Live',
          style: context.cupertinoTextTheme.textStyle.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        trailing: ValueListenableBuilder<bool>(
          valueListenable: controller.isCreatingStream,
          builder: (context, isCreating, child) {
            return CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: isCreating ? null : () => _startLiveStream(context),
              child: Text(
                'Start',
                style: TextStyle(
                  color: isCreating
                      ? CupertinoColors.systemGrey
                      : CupertinoColors.systemBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            );
          },
        ),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Preview container
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  color: CupertinoColors.black,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  children: [
                    // Camera preview would go here
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            CupertinoIcons.video_camera,
                            size: 48,
                            color: Colors.white,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Camera Preview',
                            style:
                                context.cupertinoTextTheme.textStyle.copyWith(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Camera controls
                    Positioned(
                      bottom: 16,
                      right: 16,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildCameraControl(
                            icon: CupertinoIcons.camera_rotate,
                            onPressed: controller.switchCamera,
                          ),
                          const SizedBox(width: 12),
                          _buildCameraControl(
                            icon: controller.isMuted.value
                                ? CupertinoIcons.mic_slash
                                : CupertinoIcons.mic,
                            onPressed: controller.toggleMute,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Stream title
              Text(
                'Stream Title',
                style: context.cupertinoTextTheme.textStyle.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              CupertinoTextField(
                controller: controller.titleController,
                placeholder: 'Enter your stream title...',
                maxLines: 1,
                maxLength: 100,
                decoration: BoxDecoration(
                  color: CupertinoColors.systemGrey6,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.all(12),
              ),

              const SizedBox(height: 20),

              // Stream description
              Text(
                'Description (Optional)',
                style: context.cupertinoTextTheme.textStyle.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              CupertinoTextField(
                controller: controller.descriptionController,
                placeholder: 'Tell viewers what your stream is about...',
                maxLines: 3,
                maxLength: 500,
                decoration: BoxDecoration(
                  color: CupertinoColors.systemGrey6,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.all(12),
              ),

              const SizedBox(height: 20),

              // Privacy settings
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Private Stream',
                    style: context.cupertinoTextTheme.textStyle.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  ValueListenableBuilder<bool>(
                    valueListenable: controller.isPrivate,
                    builder: (context, isPrivate, child) {
                      return CupertinoSwitch(
                        value: isPrivate,
                        onChanged: controller.togglePrivacy,
                        activeTrackColor: CupertinoColors.systemGreen,
                      );
                    },
                  ),
                ],
              ),

              const SizedBox(height: 8),

              Text(
                'Only people you choose can view your stream',
                style: context.cupertinoTextTheme.textStyle.copyWith(
                  fontSize: 14,
                  color: CupertinoColors.systemGrey,
                ),
              ),

              // Approval requirement for public streams
              ValueListenableBuilder<bool>(
                valueListenable: controller.isPrivate,
                builder: (context, isPrivate, child) {
                  if (isPrivate) return const SizedBox.shrink();

                  return Column(
                    children: [
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Require Approval',
                                  style: context.cupertinoTextTheme.textStyle
                                      .copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Viewers need your approval to join',
                                  style: context.cupertinoTextTheme.textStyle
                                      .copyWith(
                                    fontSize: 14,
                                    color: CupertinoColors.systemGrey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ValueListenableBuilder<bool>(
                            valueListenable: controller.requiresApproval,
                            builder: (context, requiresApproval, child) {
                              return CupertinoSwitch(
                                value: requiresApproval,
                                onChanged: controller.toggleApprovalRequirement,
                                activeTrackColor: CupertinoColors.systemGreen,
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),

              // Member selection for private streams
              ValueListenableBuilder<bool>(
                valueListenable: controller.isPrivate,
                builder: (context, isPrivate, child) {
                  if (!isPrivate) return const SizedBox.shrink();

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),

                      // Selected members display
                      ValueListenableBuilder<List<SBaseUser>>(
                        valueListenable: controller.selectedMembers,
                        builder: (context, selectedMembers, child) {
                          return Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: CupertinoColors.systemGrey6,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: CupertinoColors.systemGrey4,
                                width: 1,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Icon(
                                      CupertinoIcons.person_2,
                                      size: 16,
                                      color: CupertinoColors.systemGrey,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      selectedMembers.isEmpty
                                          ? 'No members selected'
                                          : '${selectedMembers.length} member${selectedMembers.length == 1 ? '' : 's'} selected',
                                      style: context
                                          .cupertinoTextTheme.textStyle
                                          .copyWith(
                                        fontSize: 14,
                                        color: CupertinoColors.systemGrey,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const Spacer(),
                                    CupertinoButton(
                                      padding: EdgeInsets.zero,
                                      onPressed: _showMemberSelection,
                                      child: Text(
                                        selectedMembers.isEmpty
                                            ? 'Select'
                                            : 'Edit',
                                        style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                if (selectedMembers.isNotEmpty) ...[
                                  const SizedBox(height: 8),
                                  Wrap(
                                    spacing: 8,
                                    runSpacing: 4,
                                    children: selectedMembers.map((user) {
                                      return Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: CupertinoColors.systemBlue
                                              .withValues(alpha: 0.1),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              user.fullName,
                                              style: context
                                                  .cupertinoTextTheme.textStyle
                                                  .copyWith(
                                                fontSize: 12,
                                                color:
                                                    CupertinoColors.systemBlue,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            const SizedBox(width: 4),
                                            GestureDetector(
                                              onTap: () => controller
                                                  .removeSelectedMember(user),
                                              child: const Icon(
                                                CupertinoIcons
                                                    .xmark_circle_fill,
                                                size: 14,
                                                color:
                                                    CupertinoColors.systemGrey,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ],
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  );
                },
              ),

              const SizedBox(height: 40),

              // Loading indicator
              ValueListenableBuilder<bool>(
                valueListenable: controller.isCreatingStream,
                builder: (context, isCreating, child) {
                  if (isCreating) {
                    return const Center(
                      child: Column(
                        children: [
                          CupertinoActivityIndicator(radius: 16),
                          SizedBox(height: 12),
                          Text('Creating your live stream...'),
                        ],
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCameraControl({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  void _startLiveStream(BuildContext context) async {
    if (controller.titleController.text.trim().isEmpty) {
      VAppAlert.showErrorSnackBar(
        message: 'Please enter a stream title',
        context: context,
      );
      return;
    }

    // Validate private stream has selected members
    if (controller.isPrivate.value &&
        controller.selectedMembers.value.isEmpty) {
      VAppAlert.showErrorSnackBar(
        message: 'Please select at least one member for private stream',
        context: context,
      );
      return;
    }

    final stream = await controller.createLiveStream();
    if (stream != null) {
      // Navigate to live stream view
      context.toPage(LiveStreamView(
        stream: stream,
        isStreamer: true,
      ));
    }
  }

  void _showMemberSelection() {
    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return MemberSelectionSheet(
          selectedMembers: controller.selectedMembers.value,
          onMembersSelected: (List<SBaseUser> members) {
            // Update the controller with selected members
            controller.selectedMembers.value = members;
          },
        );
      },
    );
  }
}
